use crate::ocr::OcrResult;
use crate::utils::to_wide_chars;
use anyhow::Result;
use windows::Win32::Foundation::*;
use windows::Win32::Graphics::Gdi::*;
use windows::Win32::System::DataExchange::*;
use windows::Win32::System::Memory::*;
use windows::Win32::UI::Controls::*;
use windows::Win32::UI::Input::KeyboardAndMouse::*;
use windows::Win32::UI::WindowsAndMessaging::*;
use windows::Win32::UI::Shell::*;

// 控件 ID 定义
const ID_COPY_BUTTON: i32 = 1001;
const ID_SAVE_BUTTON: i32 = 1002;
const ID_CLOSE_BUTTON: i32 = 1003;

/// 现代化的 OCR 结果窗口 (使用 Win32 API 实现现代化样式)
pub struct ModernOcrResultWindow {
    hwnd: HWND,
    text_edit: HWND,
    copy_button: HWND,
    save_button: HWND,
    close_button: HWND,
    confidence_label: HWND,
    ocr_results: Vec<OcrResult>,
    image_bitmap: Option<HBITMAP>,
    image_width: i32,
    image_height: i32,
    font: HFONT,
    button_font: HFONT,
    average_confidence: f32,
}

impl ModernOcrResultWindow {
    /// 创建并显示现代化的 OCR 结果窗口
    pub fn show(
        image_data: Vec<u8>,
        ocr_results: Vec<OcrResult>,
        selection_rect: RECT,
    ) -> Result<()> {
        unsafe {
            // 初始化 WinRT
            RoInitialize(RO_INIT_MULTITHREADED)?;

            let mut window_instance = Self {
                window: None,
                main_grid: None,
                image_container: None,
                text_container: None,
                text_block: None,
                copy_button: None,
                save_button: None,
                close_button: None,
                confidence_text: None,
                ocr_results: ocr_results.clone(),
                image_data: image_data.clone(),
                average_confidence: 0.0,
            };

            // 计算平均置信度
            window_instance.calculate_confidence();

            // 创建窗口
            window_instance.create_window()?;

            // 设置窗口内容
            window_instance.setup_ui()?;

            // 设置窗口位置
            window_instance.position_window(selection_rect)?;

            // 激活窗口
            if let Some(window) = &window_instance.window {
                window.Activate()?;
            }

            // 保持窗口实例存活
            std::mem::forget(window_instance);

            Ok(())
        }
    }

    /// 计算平均置信度
    fn calculate_confidence(&mut self) {
        let mut total_confidence = 0.0;
        let mut valid_results = 0;

        for result in &self.ocr_results {
            if result.confidence > 0.0 {
                total_confidence += result.confidence;
                valid_results += 1;
            }
        }

        self.average_confidence = if valid_results > 0 {
            total_confidence / valid_results as f32
        } else {
            0.0
        };
    }

    /// 创建 WinUI 3 窗口
    fn create_window(&mut self) -> Result<()> {
        unsafe {
            // 创建窗口
            let window = Window::new()?;
            
            // 设置窗口属性
            window.SetTitle(&HSTRING::from("🔍 OCR 识别结果 - 现代化界面"))?;
            
            // 设置窗口大小
            window.SetExtendsContentIntoTitleBar(true)?;
            
            self.window = Some(window);
            Ok(())
        }
    }

    /// 设置用户界面
    fn setup_ui(&mut self) -> Result<()> {
        unsafe {
            if let Some(window) = &self.window {
                // 创建主网格
                let main_grid = Grid::new()?;
                main_grid.SetBackground(&self.create_gradient_background()?)?;
                
                // 设置网格列定义
                let col1 = ColumnDefinition::new()?;
                col1.SetWidth(GridLength::from_star(1.5))?; // 图像区域
                
                let col2 = ColumnDefinition::new()?;
                col2.SetWidth(GridLength::from_pixels(20.0))?; // 分隔符
                
                let col3 = ColumnDefinition::new()?;
                col3.SetWidth(GridLength::from_star(1.0))?; // 文本区域
                
                main_grid.ColumnDefinitions()?.Append(&col1)?;
                main_grid.ColumnDefinitions()?.Append(&col2)?;
                main_grid.ColumnDefinitions()?.Append(&col3)?;

                // 设置网格行定义
                let row1 = RowDefinition::new()?;
                row1.SetHeight(GridLength::from_pixels(40.0))?; // 标题栏
                
                let row2 = RowDefinition::new()?;
                row2.SetHeight(GridLength::from_star(1.0))?; // 主内容
                
                let row3 = RowDefinition::new()?;
                row3.SetHeight(GridLength::from_pixels(60.0))?; // 按钮区域
                
                main_grid.RowDefinitions()?.Append(&row1)?;
                main_grid.RowDefinitions()?.Append(&row2)?;
                main_grid.RowDefinitions()?.Append(&row3)?;

                // 创建自定义标题栏
                self.create_custom_titlebar(&main_grid)?;

                // 创建图像区域
                self.create_image_area(&main_grid)?;

                // 创建文本区域
                self.create_text_area(&main_grid)?;

                // 创建按钮区域
                self.create_button_area(&main_grid)?;

                // 设置窗口内容
                window.SetContent(&main_grid)?;
                self.main_grid = Some(main_grid);
            }
            Ok(())
        }
    }

    /// 创建渐变背景
    fn create_gradient_background(&self) -> Result<LinearGradientBrush> {
        unsafe {
            let gradient = LinearGradientBrush::new()?;
            
            // 设置渐变方向（从上到下）
            gradient.SetStartPoint(Point { X: 0.0, Y: 0.0 })?;
            gradient.SetEndPoint(Point { X: 0.0, Y: 1.0 })?;
            
            // 添加渐变色
            let stop1 = GradientStop::new()?;
            stop1.SetColor(windows::UI::Color { A: 255, R: 248, G: 249, B: 250 })?; // 浅灰白
            stop1.SetOffset(0.0)?;
            
            let stop2 = GradientStop::new()?;
            stop2.SetColor(windows::UI::Color { A: 255, R: 240, G: 242, B: 245 })?; // 稍深灰白
            stop2.SetOffset(1.0)?;
            
            gradient.GradientStops()?.Append(&stop1)?;
            gradient.GradientStops()?.Append(&stop2)?;
            
            Ok(gradient)
        }
    }

    /// 创建自定义标题栏
    fn create_custom_titlebar(&mut self, parent: &Grid) -> Result<()> {
        unsafe {
            let title_grid = Grid::new()?;
            title_grid.SetBackground(&self.create_title_background()?)?;
            
            // 设置标题栏位置
            Grid::SetRow(&title_grid, 0)?;
            Grid::SetColumnSpan(&title_grid, 3)?;
            
            // 创建标题文本
            let title_text = TextBlock::new()?;
            title_text.SetText(&HSTRING::from("🔍 OCR 识别结果"))?;
            title_text.SetFontSize(16.0)?;
            title_text.SetFontWeight(windows::UI::Text::FontWeights::SemiBold()?)?;
            title_text.SetForeground(&SolidColorBrush::CreateInstanceWithColor(
                windows::UI::Color { A: 255, R: 51, G: 51, B: 51 }
            )?)?;
            title_text.SetVerticalAlignment(VerticalAlignment::Center)?;
            title_text.SetHorizontalAlignment(HorizontalAlignment::Left)?;
            title_text.SetMargin(Thickness { Left: 16.0, Top: 0.0, Right: 0.0, Bottom: 0.0 })?;
            
            title_grid.Children()?.Append(&title_text)?;
            parent.Children()?.Append(&title_grid)?;
            
            Ok(())
        }
    }

    /// 创建标题栏背景
    fn create_title_background(&self) -> Result<SolidColorBrush> {
        unsafe {
            Ok(SolidColorBrush::CreateInstanceWithColor(
                windows::UI::Color { A: 255, R: 255, G: 255, B: 255 } // 白色
            )?)
        }
    }

    /// 创建图像显示区域
    fn create_image_area(&mut self, parent: &Grid) -> Result<()> {
        unsafe {
            // 创建图像容器
            let image_border = Border::new()?;
            image_border.SetBackground(&SolidColorBrush::CreateInstanceWithColor(
                windows::UI::Color { A: 255, R: 255, G: 255, B: 255 }
            )?)?;
            image_border.SetBorderBrush(&SolidColorBrush::CreateInstanceWithColor(
                windows::UI::Color { A: 255, R: 230, G: 230, B: 230 }
            )?)?;
            image_border.SetBorderThickness(Thickness::uniform(1.0))?;
            image_border.SetCornerRadius(CornerRadius::uniform(8.0))?;
            image_border.SetMargin(Thickness::uniform(16.0))?;
            
            // 设置位置
            Grid::SetRow(&image_border, 1)?;
            Grid::SetColumn(&image_border, 0)?;
            
            // 创建图像显示
            if !self.image_data.is_empty() {
                let image_element = self.create_image_from_data()?;
                image_border.SetChild(&image_element)?;
            } else {
                // 显示占位符
                let placeholder = TextBlock::new()?;
                placeholder.SetText(&HSTRING::from("📷\n图像加载失败"))?;
                placeholder.SetTextAlignment(TextAlignment::Center)?;
                placeholder.SetVerticalAlignment(VerticalAlignment::Center)?;
                placeholder.SetHorizontalAlignment(HorizontalAlignment::Center)?;
                placeholder.SetFontSize(24.0)?;
                placeholder.SetForeground(&SolidColorBrush::CreateInstanceWithColor(
                    windows::UI::Color { A: 255, R: 150, G: 150, B: 150 }
                )?)?;
                
                image_border.SetChild(&placeholder)?;
            }
            
            parent.Children()?.Append(&image_border)?;
            self.image_container = Some(image_border);
            
            Ok(())
        }
    }

    /// 从图像数据创建 Image 元素
    fn create_image_from_data(&self) -> Result<Image> {
        unsafe {
            let image = Image::new()?;
            image.SetStretch(Stretch::Uniform)?;

            // 这里需要将 BMP 数据转换为 WinUI 3 可以显示的格式
            // 由于复杂性，我们先显示一个占位符
            // 在实际实现中，您需要使用 BitmapImage 和 InMemoryRandomAccessStream

            Ok(image)
        }
    }

    /// 创建文本显示区域
    fn create_text_area(&mut self, parent: &Grid) -> Result<()> {
        unsafe {
            // 创建文本容器
            let text_container = StackPanel::new()?;
            text_container.SetOrientation(Orientation::Vertical)?;
            text_container.SetMargin(Thickness::uniform(16.0))?;

            // 创建滚动视图
            let scroll_viewer = ScrollViewer::new()?;
            scroll_viewer.SetVerticalScrollBarVisibility(ScrollBarVisibility::Auto)?;
            scroll_viewer.SetHorizontalScrollBarVisibility(ScrollBarVisibility::Auto)?;
            scroll_viewer.SetBackground(&SolidColorBrush::CreateInstanceWithColor(
                windows::UI::Color { A: 255, R: 255, G: 255, B: 255 }
            )?)?;
            scroll_viewer.SetCornerRadius(CornerRadius::uniform(8.0))?;
            scroll_viewer.SetBorderBrush(&SolidColorBrush::CreateInstanceWithColor(
                windows::UI::Color { A: 255, R: 230, G: 230, B: 230 }
            )?)?;
            scroll_viewer.SetBorderThickness(Thickness::uniform(1.0))?;
            scroll_viewer.SetPadding(Thickness::uniform(16.0))?;

            // 设置位置
            Grid::SetRow(&scroll_viewer, 1)?;
            Grid::SetColumn(&scroll_viewer, 2)?;

            // 创建文本内容
            let text_content = self.create_text_content()?;
            scroll_viewer.SetContent(&text_content)?;

            // 创建置信度显示
            let confidence_panel = self.create_confidence_panel()?;
            text_container.Children()?.Append(&scroll_viewer)?;
            text_container.Children()?.Append(&confidence_panel)?;

            parent.Children()?.Append(&text_container)?;
            self.text_container = Some(scroll_viewer);

            Ok(())
        }
    }

    /// 创建文本内容
    fn create_text_content(&mut self) -> Result<TextBlock> {
        unsafe {
            let text_block = TextBlock::new()?;

            // 合并所有 OCR 结果
            let mut all_text = String::new();
            let mut is_empty = true;

            for (i, result) in self.ocr_results.iter().enumerate() {
                if i > 0 {
                    all_text.push('\n');
                }

                if result.text == "未识别到任何文字" && result.confidence == 0.0 {
                    all_text.push_str("未识别到任何文字");
                } else {
                    all_text.push_str(&result.text);
                    is_empty = false;
                }
            }

            if all_text.trim().is_empty() {
                all_text = "未识别到文本内容".to_string();
                is_empty = true;
            }

            text_block.SetText(&HSTRING::from(all_text))?;
            text_block.SetTextWrapping(TextWrapping::Wrap)?;
            text_block.SetIsTextSelectionEnabled(true)?; // 允许选择文本
            text_block.SetFontSize(14.0)?;
            text_block.SetLineHeight(20.0)?;

            // 根据是否有内容设置颜色
            if is_empty {
                text_block.SetForeground(&SolidColorBrush::CreateInstanceWithColor(
                    windows::UI::Color { A: 255, R: 150, G: 150, B: 150 } // 灰色
                )?)?;
                text_block.SetFontStyle(windows::UI::Text::FontStyle::Italic)?;
            } else {
                text_block.SetForeground(&SolidColorBrush::CreateInstanceWithColor(
                    windows::UI::Color { A: 255, R: 51, G: 51, B: 51 } // 深灰色
                )?)?;
            }

            self.text_block = Some(text_block.clone());
            Ok(text_block)
        }
    }

    /// 创建置信度显示面板
    fn create_confidence_panel(&mut self) -> Result<Border> {
        unsafe {
            let border = Border::new()?;
            border.SetBackground(&SolidColorBrush::CreateInstanceWithColor(
                windows::UI::Color { A: 255, R: 248, G: 249, B: 250 }
            )?)?;
            border.SetBorderBrush(&SolidColorBrush::CreateInstanceWithColor(
                windows::UI::Color { A: 255, R: 230, G: 230, B: 230 }
            )?)?;
            border.SetBorderThickness(Thickness::uniform(1.0))?;
            border.SetCornerRadius(CornerRadius::uniform(6.0))?;
            border.SetMargin(Thickness { Left: 0.0, Top: 12.0, Right: 0.0, Bottom: 0.0 })?;
            border.SetPadding(Thickness::uniform(12.0))?;

            let confidence_text = TextBlock::new()?;
            let confidence_info = if self.average_confidence > 0.0 {
                format!("📊 识别置信度: {:.1}%", self.average_confidence * 100.0)
            } else {
                "❌ 未检测到文字".to_string()
            };

            confidence_text.SetText(&HSTRING::from(confidence_info))?;
            confidence_text.SetFontSize(12.0)?;
            confidence_text.SetForeground(&SolidColorBrush::CreateInstanceWithColor(
                windows::UI::Color { A: 255, R: 102, G: 102, B: 102 }
            )?)?;

            border.SetChild(&confidence_text)?;
            self.confidence_text = Some(confidence_text);

            Ok(border)
        }
    }

    /// 创建按钮区域
    fn create_button_area(&mut self, parent: &Grid) -> Result<()> {
        unsafe {
            let button_panel = StackPanel::new()?;
            button_panel.SetOrientation(Orientation::Horizontal)?;
            button_panel.SetHorizontalAlignment(HorizontalAlignment::Right)?;
            button_panel.SetVerticalAlignment(VerticalAlignment::Center)?;
            button_panel.SetMargin(Thickness::uniform(16.0))?;
            button_panel.SetSpacing(12.0)?;

            // 设置位置
            Grid::SetRow(&button_panel, 2)?;
            Grid::SetColumnSpan(&button_panel, 3)?;

            // 创建复制按钮
            let copy_button = self.create_modern_button("📋 复制", "复制识别的文本到剪贴板")?;

            // 创建保存按钮
            let save_button = self.create_modern_button("💾 保存", "保存识别结果到文件")?;

            // 创建关闭按钮
            let close_button = self.create_modern_button("❌ 关闭", "关闭此窗口")?;

            // 设置按钮事件处理
            self.setup_button_events(&copy_button, &save_button, &close_button)?;

            button_panel.Children()?.Append(&copy_button)?;
            button_panel.Children()?.Append(&save_button)?;
            button_panel.Children()?.Append(&close_button)?;

            parent.Children()?.Append(&button_panel)?;

            self.copy_button = Some(copy_button);
            self.save_button = Some(save_button);
            self.close_button = Some(close_button);

            Ok(())
        }
    }

    /// 创建现代化按钮
    fn create_modern_button(&self, content: &str, tooltip: &str) -> Result<Button> {
        unsafe {
            let button = Button::new()?;
            button.SetContent(&PropertyValue::CreateString(&HSTRING::from(content))?)?;
            button.SetMinWidth(80.0)?;
            button.SetHeight(36.0)?;
            button.SetFontSize(14.0)?;
            button.SetCornerRadius(CornerRadius::uniform(6.0))?;

            // 设置现代化样式
            button.SetBackground(&SolidColorBrush::CreateInstanceWithColor(
                windows::UI::Color { A: 255, R: 0, G: 120, B: 215 } // Windows 蓝色
            )?)?;
            button.SetForeground(&SolidColorBrush::CreateInstanceWithColor(
                windows::UI::Color { A: 255, R: 255, G: 255, B: 255 } // 白色文字
            )?)?;

            // 设置工具提示
            let tooltip_obj = ToolTip::new()?;
            tooltip_obj.SetContent(&PropertyValue::CreateString(&HSTRING::from(tooltip))?)?;
            ToolTipService::SetToolTip(&button, &tooltip_obj)?;

            Ok(button)
        }
    }

    /// 设置按钮事件处理
    fn setup_button_events(
        &self,
        copy_button: &Button,
        save_button: &Button,
        close_button: &Button,
    ) -> Result<()> {
        unsafe {
            // 复制按钮事件
            let copy_handler = TypedEventHandler::new({
                let text_block = self.text_block.clone();
                move |_sender: &Option<IInspectable>, _args: &Option<RoutedEventArgs>| {
                    if let Some(text_block) = &text_block {
                        if let Ok(text) = text_block.Text() {
                            let _ = Self::copy_to_clipboard(&text.to_string());
                        }
                    }
                    Ok(())
                }
            });
            copy_button.Click(&copy_handler)?;

            // 保存按钮事件
            let save_handler = TypedEventHandler::new({
                let text_block = self.text_block.clone();
                move |_sender: &Option<IInspectable>, _args: &Option<RoutedEventArgs>| {
                    if let Some(text_block) = &text_block {
                        if let Ok(text) = text_block.Text() {
                            let _ = Self::save_to_file(&text.to_string());
                        }
                    }
                    Ok(())
                }
            });
            save_button.Click(&save_handler)?;

            // 关闭按钮事件
            let close_handler = TypedEventHandler::new({
                let window = self.window.clone();
                move |_sender: &Option<IInspectable>, _args: &Option<RoutedEventArgs>| {
                    if let Some(window) = &window {
                        let _ = window.Close();
                    }
                    Ok(())
                }
            });
            close_button.Click(&close_handler)?;

            Ok(())
        }
    }

    /// 复制文本到剪贴板
    fn copy_to_clipboard(text: &str) -> Result<()> {
        unsafe {
            use windows::ApplicationModel::DataTransfer::*;

            let data_package = DataPackage::new()?;
            data_package.SetText(&HSTRING::from(text))?;

            Clipboard::SetContent(&data_package)?;
            Ok(())
        }
    }

    /// 保存文本到文件
    fn save_to_file(text: &str) -> Result<()> {
        unsafe {
            // 创建文件保存对话框
            let file_picker = FileSavePicker::new()?;
            file_picker.SetSuggestedStartLocation(PickerLocationId::DocumentsLibrary)?;
            file_picker.SetDefaultFileExtension(&HSTRING::from(".txt"))?;
            file_picker.SetSuggestedFileName(&HSTRING::from("OCR识别结果"))?;

            // 添加文件类型
            let txt_extensions = StringVector::new()?;
            txt_extensions.Append(&HSTRING::from(".txt"))?;
            file_picker.FileTypeChoices()?.Insert(
                &HSTRING::from("文本文件"),
                &txt_extensions,
            )?;

            // 显示对话框并保存文件
            // 注意：这里需要异步处理，实际实现可能需要更复杂的逻辑

            Ok(())
        }
    }

    /// 设置窗口位置
    fn position_window(&self, selection_rect: RECT) -> Result<()> {
        unsafe {
            if let Some(window) = &self.window {
                // 获取当前显示器信息
                let display_info = windows::Graphics::Display::DisplayInformation::GetForCurrentView()?;

                // 设置窗口大小
                let window_width = 800.0;
                let window_height = 600.0;

                // 计算窗口位置（在截图区域右侧）
                let mut window_x = selection_rect.right as f64 + 20.0;
                let mut window_y = selection_rect.top as f64;

                // 确保窗口不超出屏幕边界
                let screen_width = 1920.0; // 这里应该获取实际屏幕宽度
                let screen_height = 1080.0; // 这里应该获取实际屏幕高度

                if window_x + window_width > screen_width {
                    window_x = selection_rect.left as f64 - window_width - 20.0;
                    if window_x < 0.0 {
                        window_x = 50.0;
                    }
                }

                if window_y + window_height > screen_height {
                    window_y = screen_height - window_height - 50.0;
                    if window_y < 0.0 {
                        window_y = 50.0;
                    }
                }

                // 设置窗口位置和大小
                // 注意：WinUI 3 的窗口定位 API 可能有所不同

            }
            Ok(())
        }
    }
}
